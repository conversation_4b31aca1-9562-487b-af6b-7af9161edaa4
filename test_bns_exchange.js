// Simple test to verify BNS exchange fix
const { PlayMode } = require('./lib/skywind/services/playMode');

// Test data similar to the failing test
const bnsGameData = {
    gameTokenData: {
        playmode: "bns",
        currency: "USD",
    },
    bnsPromotion: {
        exchangeRate: 0.01
    }
};

console.log('Testing BNS exchange...');

try {
    // This should return 1 (100 BNS * 0.01 = 1 USD)
    const result1 = PlayMode.exchange(bnsGameData, 100, "USD");
    console.log(`PlayMode.exchange(bnsGameData, 100, "USD") = ${result1}`);
    console.log(`Expected: 1, Actual: ${result1}, ${result1 === 1 ? 'PASS' : 'FAIL'}`);

    // This should return 100 (1 BNS / 0.01 = 100 USD)
    const result2 = PlayMode.exchange(bnsGameData, 1, "BNS");
    console.log(`PlayMode.exchange(bnsGameData, 1, "BNS") = ${result2}`);
    console.log(`Expected: 100, Actual: ${result2}, ${result2 === 100 ? 'PASS' : 'FAIL'}`);

} catch (error) {
    console.error('Error running test:', error.message);
}
